"use client";

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo, ChangeEvent, KeyboardEvent } from "react";
// Removed unused useRouter
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Bot, User as UserIcon, Paperclip, FileText, Download, CheckCircle2, X, Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { MAX_FILE_SIZE, validateFileExtension, formatFileSize, ALLOWED_FILE_EXTENSIONS } from "@/lib/file-utils";
import { format } from "date-fns";
import { useAuth, User } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// --- Types ---
interface GeminiMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  attachments?: FileAttachment[];
}

interface FileAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  status: "uploading" | "uploaded" | "failed";
  content?: string; // For text-based files
}

interface ChatSession {
  id: string;
  title: string;
  messages: GeminiMessage[];
  createdAt: string;
  updatedAt: string;
}

// --- Constants ---

const MAX_FILES_PER_MESSAGE = 5;
const GEMINI_MODEL = "gemini-1.5-pro";

// --- File Preview Component ---
const FilePreview = React.memo(({
  file,
  onRemove
}: {
  file: FileAttachment;
  onRemove: () => void;
}) => {
  const getFileIcon = (type: string) => {
    if (type.includes("pdf")) return "📄";
    if (type.includes("json")) return "📋";
    if (type.includes("xml") || type.includes("html")) return "🌐";
    if (type.includes("text") || type.includes("txt")) return "📝";
    if (type.includes("csv")) return "📊";
    return "📎";
  };

  return (
    <Card className="p-3">
      <div className="flex items-center gap-3">
        <div className="text-2xl">{getFileIcon(file.type)}</div>
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">{file.name}</div>
          <div className="text-xs text-muted-foreground">
            {formatFileSize(file.size)}
          </div>
        </div>
        <div className="flex items-center gap-2">
          {file.status === "uploading" && (
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
          )}
          {file.status === "uploaded" && (
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          )}
          {file.status === "failed" && (
            <AlertCircle className="h-4 w-4 text-red-500" />
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Card>
  );
});

FilePreview.displayName = "FilePreview";

// --- Message Component ---
const ChatMessage = React.memo(({ message }: { message: GeminiMessage }) => {
  const isUser = message.role === "user";

  return (
    <div className={cn("flex gap-3 p-4", isUser ? "flex-row-reverse" : "flex-row")}>
      <div className={cn(
        "flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border",
        isUser ? "bg-primary shadow-sm text-primary-foreground" : "bg-muted"
      )}>
        {isUser ? <UserIcon className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </div>

      <div className={cn("flex flex-col space-y-2 max-w-[80%]", isUser && "items-end")}>
        <div className={cn(
          "rounded-lg px-3 py-2 text-sm",
          isUser
            ? "bg-primary shadow-sm shadow-purple-300 text-primary-foreground ml-auto"
            : "bg-white dark:bg-slate-700 shadow-sm p-4"
        )}>
          {/* Prevent empty messages from collapsing */}
          <div className="whitespace-pre-wrap break-words min-h-[1em]">
            {message.content}
          </div>
        </div>

        {message.attachments && message.attachments.length > 0 && (
          <div className="space-y-2 w-full">
            {message.attachments.map((attachment) => (
              <div key={attachment.id} className="text-xs">
                <Badge variant="secondary" className="gap-1">
                  <FileText className="h-3 w-3" />
                  {attachment.name}
                </Badge>
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          {format(new Date(message.timestamp), "HH:mm")}
        </div>
      </div>
    </div>
  );
});

ChatMessage.displayName = "ChatMessage";

// --- Main Component ---
export const GeminiChatInterface = React.memo(() => {
  const { user } = useAuth();
  const isMobile = useIsMobile();

  // State
  const [messages, setMessages] = useState<GeminiMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [attachments, setAttachments] = useState<FileAttachment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // File handling
  const handleFilesSelected = useCallback(async (files: File[]) => {
    const newAttachments: FileAttachment[] = files.map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      name: file.name,
      size: file.size,
      type: file.type || "application/octet-stream",
      status: "uploading" as const
    }));

    setAttachments(prev => [...prev, ...newAttachments]);

    // Process files
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const attachment = newAttachments[i];

      try {
        // Read file content for text-based files locally
        if (file.type.startsWith("text/") ||
            [".txt", ".json", ".xml", ".csv", ".md", ".html"].some(ext => file.name.endsWith(ext))) {

          const content = await file.text();
          setAttachments(prev => prev.map(att =>
            att.id === attachment.id
              ? { ...att, status: "uploaded", content }
              : att
          ));
        } else {
          // For binary files, upload to a server endpoint to be processed
          const formData = new FormData();
          formData.append("file", file);

          const idToken = await user?.getIdToken();
          const response = await fetch("/api/gemini-upload", {
            method: "POST",
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
            body: formData,
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ message: "Upload failed" }));
            throw new Error(errorData.message);
          }

          const result = await response.json();
          setAttachments(prev => prev.map(att =>
            att.id === attachment.id
              ? { ...att, status: "uploaded", url: result.fileUrl, content: result.extractedText }
              : att
          ));
        }
      } catch (error) {
        console.error("File processing error:", error);
        setAttachments(prev => prev.map(att =>
          att.id === attachment.id
            ? { ...att, status: "failed" }
            : att
        ));
        toast.error(`Failed to process ${file.name}`);
      }
    }
  }, [user]);

  const handleFileValidation = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    let validFiles: File[] = [];

    for (const file of fileArray) {
      if (!validateFileExtension(file.name)) {
        toast.error(`${file.name}: Unsupported file type.`);
        continue;
      }
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`${file.name}: File is too large (max ${formatFileSize(MAX_FILE_SIZE)}).`);
        continue;
      }
      validFiles.push(file);
    }

    if (attachments.length + validFiles.length > MAX_FILES_PER_MESSAGE) {
      toast.error(`You can only attach a maximum of ${MAX_FILES_PER_MESSAGE} files.`);
      return;
    }

    if (validFiles.length > 0) {
      handleFilesSelected(validFiles);
    }
  }, [attachments.length, handleFilesSelected]);


  const removeAttachment = useCallback((id: string) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  }, []);

  // Message sending with streaming
  const sendMessage = useCallback(async () => {
    if ((!inputValue.trim() && attachments.length === 0) || isLoading) return;

    const messageContent = inputValue.trim();
    const messageAttachments = attachments.filter(att => att.status === "uploaded");

    if (attachments.some(att => att.status === "failed")) {
      toast.error("Please remove failed attachments before sending.");
      return;
    }

    if (attachments.some(att => att.status === "uploading")) {
      toast.error("Please wait for all files to finish uploading.");
      return;
    }

    const userMessage: GeminiMessage = {
      id: `user-${Date.now()}`,
      role: "user",
      content: messageContent || "[Files attached]",
      timestamp: new Date().toISOString(),
      attachments: messageAttachments,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");
    setAttachments([]);
    setIsLoading(true);

    const assistantMessageId = `assistant-${Date.now()}`;
    const assistantMessage: GeminiMessage = {
      id: assistantMessageId,
      role: "assistant",
      content: "",
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      const idToken = await user?.getIdToken();
      const response = await fetch("/api/gemini-chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          message: messageContent,
          attachments: messageAttachments,
          conversationHistory: messages.slice(-10), // Send last 10 messages for context
        }),
      });

      if (!response.ok || !response.body) {
        const errorData = await response.json().catch(() => ({ message: "Failed to get AI response." }));
        throw new Error(errorData.message);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedContent = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunkText = decoder.decode(value, { stream: true });
        accumulatedContent += chunkText;

        setMessages(prev => prev.map(msg =>
          msg.id === assistantMessageId
            ? { ...msg, content: accumulatedContent }
            : msg
        ));

        // Small delay to ensure UI updates are visible
        await new Promise(resolve => setTimeout(resolve, 1));
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
      console.error("Error sending message:", error);
      toast.error(errorMessage);

      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, content: `I apologize, but an error occurred: ${errorMessage}` }
          : msg
      ));
    } finally {
      setIsLoading(false);
      textareaRef.current?.focus();
    }
  }, [inputValue, attachments, messages, user, isLoading]);

  const handleKeyPress = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  const handleInputChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  }, []);

  // Drag and drop handlers
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    if (isLoading) return;
    handleFileValidation(e.dataTransfer.files);
  }, [handleFileValidation, isLoading]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!isLoading) setIsDragOver(true);
  }, [isLoading]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  // Welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: GeminiMessage = {
        id: "welcome",
        role: "assistant",
        content: "Hello! I'm your AI assistant powered by Gemini. I can help you analyze pentest scan reports, security documents, and answer questions about cybersecurity. Feel free to upload your files and ask me anything!",
        timestamp: new Date().toISOString()
      };
      setMessages([welcomeMessage]);
    }
  }, []); // Run only once on mount

  return (
    <div className="flex flex-col h-full max-h-[calc(100vh-4rem)] bg-background">
      {/* Header */}
      <div className="bg-transparent p-6 max-w-5xl mx-auto w-full">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Bot className="p-1.5 bg-purple-100 border border-purple-300 rounded-xl h-10 w-10 text-primary" />
            </div>
            <h2 className="text-xl font-semibold">Chat Assistant</h2>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-0 max-w-5xl mx-auto w-full">
        <div className="space-y-0 px-4">
          {messages.map((message) => (
            <ChatMessage key={message.id} message={message} />
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t bg-background/80 backdrop-blur-sm p-4 space-y-4">
        {/* File Attachments */}
        {attachments.length > 0 && (
          <div className="max-w-5xl mx-auto">
            <div className="grid gap-2 max-h-36 overflow-y-auto px-1">
              {attachments.map((attachment) => (
                <FilePreview
                  key={attachment.id}
                  file={attachment}
                  onRemove={() => removeAttachment(attachment.id)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Message Input with Integrated File Upload */}
        <div className="flex items-end gap-2 max-w-5xl mx-auto">
          <div className="flex-1 relative">
            <div
              className={cn(
                "relative flex items-end rounded-2xl border bg-background transition-colors",
                isDragOver ? "border-primary bg-primary/5" : "border-input",
                "focus-within:border-primary focus-within:ring-1 focus-within:ring-primary"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <Textarea
                ref={textareaRef}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                placeholder="Ask me about your pentest reports or security questions..."
                className="flex-1 my-auto min-h-[50px] max-h-[200px] resize-none border-0 bg-transparent py-3 pl-3 pr-12 text-sm placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                disabled={isLoading}
                rows={1}
              />
              <div className="absolute bottom-3 right-3 flex items-center gap-1">
                <input
                  type="file"
                  multiple
                  accept={ALLOWED_FILE_EXTENSIONS.join(",")}
                  className="hidden"
                  ref={fileInputRef}
                  onChange={(e) => handleFileValidation(e.target.files)}
                  disabled={isLoading || attachments.length >= MAX_FILES_PER_MESSAGE}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isLoading || attachments.length >= MAX_FILES_PER_MESSAGE}
                  className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted/50"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <Button
            onClick={sendMessage}
            disabled={isLoading || (!inputValue.trim() && attachments.length === 0)}
            size="sm"
            className="h-[52px] px-3 shrink-0"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground text-center">
          Press Enter to send, Shift+Enter for a new line. You can also drag and drop files.
        </div>
      </div>
    </div>
  );
});

GeminiChatInterface.displayName = "GeminiChatInterface";