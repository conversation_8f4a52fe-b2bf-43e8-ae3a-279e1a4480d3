@import "tailwindcss";
@reference "../tailwind.config.css";
@import "../styles/typing-indicator.css";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 261 89% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 100% 98%;
    --secondary-foreground: 240 10% 16%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 261 89% 60%;
    --radius: 0.75rem;

    /* GrowthGuard Brand Colors */
    --violet: 261 89% 60%;
    --violet-light: 261 89% 80%;
    --violet-lighter: 261 60% 90%;
    --violet-lightest: 270 100% 98%;
    --dark: 240 10% 16%;
    --dark-deeper: 222 24% 11%;
    --dark-deepest: 240 100% 4%;
    --gray: 240 4% 96%;
  }

  .dark {
    --background: 222 47% 11%; /* Explicitly set to #111827 */
    --foreground: 210 40% 98%;
    --card: 215 25% 22%;
    --card-foreground: 210 40% 98%;
    --popover: 215 28% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 261 89% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 30% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 261 89% 60%;

    /* GrowthGuard Brand Colors - Dark Theme */
    --violet: 261 89% 60%;
    --violet-light: 261 89% 80%;
    --violet-lighter: 261 60% 90%;
    --violet-lightest: 270 100% 98%;
    --dark: 240 10% 16%;
    --dark-deeper: 222 24% 11%;
    --dark-deepest: 240 100% 4%;
    --gray: 240 4% 96%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Hide scrollbar for the entire body */
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  body::-webkit-scrollbar {
    display: none;
  }
}

/* Modern SaaS Styling */
.saas-gradient-bg {
  @apply bg-gradient-to-br from-primary/10 via-background to-background;
}

/* GrowthGuard gradient background */
.growthguard-gradient-bg {
  background: linear-gradient(to bottom right, #FFFFFF, #F5EFFF);
}

.dark .growthguard-gradient-bg {
  background: #111827;
}

.glass-card {
  @apply bg-white/80 dark:bg-black/50 backdrop-blur-sm border border-white/20 dark:border-white/10;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-lg cursor-pointer;
}

/* GrowthGuard Brand Styling */
.brand-gradient {
  @apply bg-gradient-to-br from-violet-light via-violet to-violet-dark;
}

.brand-shield {
  filter: drop-shadow(0 4px 6px rgba(132, 61, 245, 0.2));
}

.logo-shadow {
  filter: drop-shadow(0 4px 8px rgba(33, 32, 52, 0.2));
}

.brand-button {
  @apply bg-violet hover:bg-violet-dark text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.brand-button-outline {
  @apply border-2 border-violet text-violet hover:bg-violet hover:text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.brand-card {
  @apply bg-white dark:bg-dark-deeper rounded-lg border border-violet-lightest dark:border-violet/20 shadow-md hover:shadow-lg transition-shadow duration-200;
}

/* Typography Utility Classes */
.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-extrabold {
  font-weight: 800;
}

/* Heading Typography */
h1, .h1 {
  font-size: 2.5rem;
  line-height: 1.2;
  font-weight: 600; /* Semi Bold instead of Extra Bold */
}

h2, .h2 {
  font-size: 2rem;
  line-height: 1.25;
  font-weight: 600; /* Semi Bold instead of Extra Bold */
}

h3, .h3 {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 600; /* Semi Bold */
}

h4, .h4 {
  font-size: 1.25rem;
  line-height: 1.4;
  font-weight: 600; /* Semi Bold */
}

h5, .h5 {
  font-size: 1.125rem;
  line-height: 1.5;
  font-weight: 500; /* Medium */
}

h6, .h6 {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 500; /* Medium */
}

/* GrowthGuard Brand Colors Utility Classes */
.text-violet {
  color: #843DF5;
}

.text-dark-shield {
  color: #212034;
}

.bg-dark-shield {
  background-color: #212034;
}

.text-violet-light {
  color: #C29EFA;
}

.text-violet-lighter {
  color: #E0CFFC;
}

.text-violet-lightest {
  color: #F5EFFF;
}

.bg-violet {
  background-color: #843DF5;
}

.bg-violet-light {
  background-color: #C29EFA;
}

.bg-violet-lighter {
  background-color: #E0CFFC;
}

.bg-violet-lightest {
  background-color: #F5EFFF;
}

.border-violet {
  border-color: #843DF5;
}

.hover\:bg-violet:hover {
  background-color: #843DF5;
}

.hover\:text-violet:hover {
  color: #843DF5;
}

.focus\:ring-violet:focus {
  --tw-ring-color: #843DF5;
}

/* Status-specific card hover effects */
.card-hover-pending {
  @apply hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-700/50;
}

.card-hover-in-progress {
  @apply hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-700/50;
}

.card-hover-re-test {
  @apply hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-700/50;
}

.card-hover-completed {
  @apply hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50 hover:border-gray-300/50 dark:hover:border-gray-700/50;
}

.chat-message-user {
  @apply bg-primary/10 dark:bg-[#843DF5] text-foreground dark:text-white rounded-2xl rounded-tr-sm shadow-sm dark:shadow-black/20;
}

.chat-message-assistant {
  @apply bg-white dark:bg-[#1E293B] text-foreground dark:text-gray-100 rounded-2xl rounded-tl-sm shadow-sm dark:shadow-black/20;
}

/* Base scan card styles */
.scan-card {
  @apply transition-all duration-300 rounded-xl overflow-hidden relative backdrop-blur-[2px] mb-4 bg-white dark:bg-transparent;
}

.scan-card::before {
  @apply content-[''] absolute left-0 top-0 bottom-0 w-1 transition-colors duration-300;
}

/* Status dots */
.status-dot-in-progress {
  @apply bg-blue-500;
}

.status-dot-critical {
  @apply bg-red-500;
}

.status-dot-pending {
  @apply bg-yellow-500;
}

.status-dot-re-test {
  @apply bg-violet;
}

/* Status change animations */
@keyframes pulse-highlight {
  0% { box-shadow: 0 0 0 0 rgba(var(--highlight-color), 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(var(--highlight-color), 0); }
  100% { box-shadow: 0 0 0 0 rgba(var(--highlight-color), 0); }
}

@keyframes badge-scale {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.status-change-animation {
  animation: pulse-highlight 1s ease-in-out;
}

.status-badge-animation {
  animation: badge-scale 0.5s ease-in-out;
}

.status-change-animation-pending {
  --highlight-color: 234, 179, 8;
}

.status-change-animation-in-progress {
  --highlight-color: 59, 130, 246;
}

.status-change-animation-completed {
  --highlight-color: 34, 197, 94;
}

.status-change-animation-critical {
  --highlight-color: 239, 68, 68;
}

.status-change-animation-re-test {
  --highlight-color: 132, 61, 245;
}

/* Glass effect for cards */
.glass-effect {
  @apply bg-white dark:bg-gray-800/90 backdrop-blur-sm border-white/40 dark:border-gray-700/40;
}

.scan-card-pending {
  @apply border-yellow-500/30 dark:border-yellow-600/30 shadow-sm shadow-yellow-500/10 dark:shadow-yellow-600/10 hover:shadow-md hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50;
}

.scan-card-pending::before {
  @apply bg-yellow-500 dark:bg-yellow-600;
}

.scan-card-in-progress {
  @apply border-blue-500/30 dark:border-blue-600/30 shadow-sm shadow-blue-500/10 dark:shadow-blue-600/10 hover:shadow-md hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50;
}

.scan-card-in-progress::before {
  @apply bg-blue-500 dark:bg-blue-600;
}

.scan-card-completed {
  @apply border-green-500/30 dark:border-green-600/30 shadow-sm shadow-green-500/10 dark:shadow-green-600/10 hover:shadow-md hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50;
}

.scan-card-completed::before {
  @apply bg-green-500 dark:bg-green-600;
}

.scan-card-critical {
  @apply border-red-500/30 dark:border-red-600/30 shadow-sm shadow-red-500/10 dark:shadow-red-600/10 hover:shadow-md hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50;
}

.scan-card-critical::before {
  @apply bg-red-500 dark:bg-red-600;
}

.scan-card-re-test {
  @apply border-violet/30 dark:border-violet/30 shadow-sm shadow-violet/10 dark:shadow-violet/10 hover:shadow-md hover:shadow-gray-300/50 dark:hover:shadow-gray-700/50;
}

.scan-card-re-test::before {
  @apply bg-violet dark:bg-violet;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/20 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/30;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 2px rgba(132, 61, 245, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(132, 61, 245, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(132, 61, 245, 0.3));
  }
}

/* Defer non-critical animations with content-visibility */
.logo-glow {
  content-visibility: auto;
  contain-intrinsic-size: auto 1em;
  animation: none; /* Disable animation initially */
}

/* Only apply animation when page is fully loaded */
.js-loaded .logo-glow {
  animation: logoGlow 2s infinite ease-in-out;
}

.logo-hover:hover {
  animation: logoGlow 2s infinite ease-in-out;
}

/* Dashboard grid layout */
.dashboard-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Performance optimization classes */
.priority-content {
  content-visibility: visible;
  contain-intrinsic-size: auto;
}

.defer-animation {
  animation: none !important;
}

.content-loaded .defer-animation {
  animation: fadeIn 0.3s ease-in-out !important;
}

/* Use content-visibility for non-visible elements */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Optimize rendering for LCP */
.text-2xl {
  font-display: swap;
}

/* Thin scrollbar styles */
/* For WebKit browsers (Chrome, Safari, newer versions of Opera) */
::-webkit-scrollbar {
  width: 3px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(132, 61, 245, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(132, 61, 245, 0.5);
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(132, 61, 245, 0.3) transparent;
}

/* Specific scrollbar styles for chat interface and history area */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.chat-scrollbar::-webkit-scrollbar {
  width: 6px; /* Make the scrollbar slimmer */
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 3px; /* Adjust border-radius for slimmer thumb */
  border: 1px solid var(--scrollbar-track);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

/* Modern Checkbox Animations */
@keyframes checkbox-check {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(45deg);
    opacity: 1;
  }
}

@keyframes checkbox-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.checkbox-check-animation {
  animation: checkbox-check 0.2s ease-in-out;
}

.checkbox-bounce-animation {
  animation: checkbox-bounce 0.15s ease-in-out;
}

/* Selection Controls Animations */
@keyframes selection-slide-in {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes selection-counter-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.selection-controls-enter {
  animation: selection-slide-in 0.3s ease-out;
}

.selection-counter-pulse {
  animation: selection-counter-pulse 0.2s ease-in-out;
}

/* Vulnerability Card Selection States */
.vulnerability-card-selected {
  @apply ring-2 ring-violet/30 border-violet/40 bg-violet/5 dark:bg-violet/10;
  box-shadow: 0 4px 12px rgba(132, 61, 245, 0.15);
  transform: translateY(-1px);
}

.vulnerability-card-hover {
  @apply hover:shadow-md;
  transition: all 0.2s ease-in-out;
}

/* Rettest Reqest Panel */
.bulk-actions-panel {
  @apply bg-gradient-to-r from-violet/5 to-violet/10 border border-violet/20;
  backdrop-filter: blur(4px);
  animation: selection-slide-in 0.3s ease-out;
}

/* Enhanced Focus States */
.modern-focus {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-violet/50 focus-visible:ring-offset-2;
}

/* Touch Target Improvements */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .modern-checkbox {
    min-height: 32px;
    min-width: 32px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .checkbox-check-animation,
  .checkbox-bounce-animation,
  .selection-controls-enter,
  .selection-counter-pulse {
    animation: none;
  }

  .vulnerability-card-selected,
  .vulnerability-card-hover {
    transform: none;
    transition: none;
  }
}

/* Enhanced Vulnerability Card Animations */
@keyframes card-entrance {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes severity-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes badge-hover {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes note-slide-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.vulnerability-card-entrance {
  animation: card-entrance 0.4s ease-out;
}

.severity-indicator-pulse {
  animation: severity-pulse 2s ease-in-out infinite;
}

.badge-hover-animation:hover {
  animation: badge-hover 0.3s ease-in-out;
}

.note-slide-animation {
  animation: note-slide-in 0.3s ease-out;
}

/* Enhanced Search and Filter Animations */
@keyframes search-focus {
  from {
    box-shadow: 0 0 0 0 rgba(132, 61, 245, 0);
  }
  to {
    box-shadow: 0 0 0 3px rgba(132, 61, 245, 0.1);
  }
}

.search-focus-animation:focus {
  animation: search-focus 0.2s ease-out;
}

/* Staggered List Animation */
.stagger-animation {
  animation: slideUp 0.4s ease-out;
}

.stagger-animation:nth-child(1) { animation-delay: 0ms; }
.stagger-animation:nth-child(2) { animation-delay: 50ms; }
.stagger-animation:nth-child(3) { animation-delay: 100ms; }
.stagger-animation:nth-child(4) { animation-delay: 150ms; }
.stagger-animation:nth-child(5) { animation-delay: 200ms; }
.stagger-animation:nth-child(6) { animation-delay: 250ms; }
.stagger-animation:nth-child(7) { animation-delay: 300ms; }
.stagger-animation:nth-child(8) { animation-delay: 350ms; }
.stagger-animation:nth-child(9) { animation-delay: 400ms; }
.stagger-animation:nth-child(10) { animation-delay: 450ms; }

/* Enhanced Button Hover Effects */
.button-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Glass Effect Enhancements */
.glass-card-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card-enhanced {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading State Animations */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

.dark .skeleton-loading {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200px 100%;
}

@layer base {
  @keyframes-in {
    from {
      opacity: 0;
      transform: translate(-50%, -48%) scale(0.96);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }
  @keyframes-out {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -48%) scale(0.96);
    }
  }
  .animate-in {
    animation: keyframes-in 0.2s ease-out;
  }
  .animate-out {
    animation: keyframes-out 0.2s ease-in;
  }
}

@layer utilities {
  .auth-page-gradient {
    background: linear-gradient(135deg, hsl(var(--background)), hsl(var(--primary) / 0.1));
  }
  .dark .auth-page-gradient {
    background: linear-gradient(135deg, hsl(var(--background)), hsl(var(--primary) / 0.05));
  }
  .auth-card {
    @apply bg-background/80 backdrop-blur-sm border;
  }
  .input-icon {
    @apply absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground;
  }
}

@keyframes slide-up-fade {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes icon-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.animate-slide-up-fade {
  animation: slide-up-fade 0.5s ease-out forwards;
}

.animate-icon-pulse {
  animation: icon-pulse 2.5s ease-in-out infinite;
}

.fullscreen-iframe-container {
  transition: all 0.3s ease-in-out;
}

/* Streaming Text Animations */
@keyframes stream-line-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-cursor {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.stream-line-animation {
  animation: stream-line-in 0.4s ease-out forwards;
  opacity: 0;
}

.typing-cursor {
  animation: typing-cursor 1s infinite;
}

/* Smooth streaming effect */
.streaming-text-container {
  overflow: hidden;
}

.streaming-line {
  animation: stream-line-in 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}
