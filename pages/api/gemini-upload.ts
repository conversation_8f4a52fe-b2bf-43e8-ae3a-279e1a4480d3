import type { NextApiRequest, NextApiResponse } from "next";
import formidable, { Fields, Files, File } from "formidable";
import fs from "fs";
import path from "path";
import { auth, getBucket } from "@/lib/firebase-admin";
import pdfParse from "pdf-parse";
import {
  MAX_FILE_SIZE,
  validateFileSize,
  validateFileExtension,
  getContentType,
  formatFileSize,
  getFileErrorMessage,
  generateUniqueFileName
} from "@/lib/file-utils";

// Disable Next.js body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

interface UploadResponse {
  message?: string;
  fileUrl?: string;
  extractedText?: string;
  error?: string;
}

// Helper function to extract text from different file types
async function extractTextFromFile(file: File): Promise<string> {
  const ext = path.extname(file.originalFilename || "").toLowerCase();
  
  try {
    if (ext === ".pdf") {
      const buffer = fs.readFileSync(file.filepath);
      const pdfData = await pdfParse(buffer);
      return pdfData.text;
    }
    
    if ([".txt", ".json", ".xml", ".html", ".csv", ".md"].includes(ext)) {
      return fs.readFileSync(file.filepath, "utf-8");
    }
    
    if ([".doc", ".docx"].includes(ext)) {
      // For Word documents, we'll return a placeholder for now
      // In production, you might want to use a library like mammoth.js
      return `[Word document: ${file.originalFilename}]\nContent extraction for Word documents is not yet implemented. Please convert to PDF or text format for full analysis.`;
    }
    
    return `[Binary file: ${file.originalFilename}]\nText extraction not supported for this file type.`;
    
  } catch (error) {
    console.error("Error extracting text from file:", error);
    return `[Error extracting text from: ${file.originalFilename}]`;
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<UploadResponse>
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Authentication failed. Please log in again." });
  }

  const userId = decodedToken.uid;

  // Configure formidable with file size limit
  const form = formidable({
    maxFileSize: MAX_FILE_SIZE,
  });

  form.parse(req, async (err: Error | null, _fields: Fields, files: Files) => {
    // Handle formidable errors
    if (err) {
      console.error("Error parsing form data:", err);
      if (err.message && err.message.includes("maxFileSize")) {
        return res.status(413).json({
          error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
        });
      }
      return res.status(500).json({ error: "Error parsing form data" });
    }

    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    // Validate file exists
    if (!file || !file.filepath || !file.originalFilename) {
      return res.status(400).json({ error: "Invalid file uploaded" });
    }

    // Validate file extension
    if (!validateFileExtension(file.originalFilename)) {
      return res.status(400).json({
        error: "Invalid file type. Supported types: PDF, TXT, JSON, XML, HTML, CSV, MD, DOC, DOCX"
      });
    }

    // Double-check file size
    if (!validateFileSize(file.size)) {
      return res.status(413).json({
        error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
      });
    }

    try {
      // Extract text content from the file
      const extractedText = await extractTextFromFile(file);
      
      // Get bucket from centralized module
      const bucket = getBucket();

      // Generate a unique filename
      const fileName = generateUniqueFileName(file.originalFilename);
      const filePath = `gemini-uploads/${userId}/${fileName}`;

      // Get content type based on file extension
      const contentType = getContentType(file.originalFilename);

      // Create write stream
      const fileUploadStream = bucket.file(filePath).createWriteStream({
        metadata: {
          contentType: contentType,
        },
        resumable: false,
      });

      // Set up error handling for the stream
      fileUploadStream.on('error', (error) => {
        console.error("Stream error during file upload:", error);
        throw error;
      });

      // Use streaming instead of reading entire file into memory
      const readStream = fs.createReadStream(file.filepath);

      // Handle read stream errors
      readStream.on('error', (error) => {
        console.error("Error reading file:", error);
        throw error;
      });

      // Pipe the file to Firebase Storage
      await new Promise<void>((resolve, reject) => {
        readStream.pipe(fileUploadStream)
          .on('finish', () => resolve())
          .on('error', reject);
      });

      // Get the download URL
      const [url] = await bucket.file(filePath).getSignedUrl({
        action: "read",
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      // Return success response with URL and extracted text
      res.status(200).json({
        message: "File uploaded successfully",
        fileUrl: url,
        extractedText: extractedText
      });
      
    } catch (error: any) {
      console.error("Error uploading file:", error);
      const errorMessage = getFileErrorMessage(error);
      res.status(500).json({ error: errorMessage });
    } finally {
      // Clean up the temporary file
      fs.unlink(file.filepath, (unlinkErr) => {
        if (unlinkErr) {
          console.error("Error deleting temporary file:", unlinkErr);
        }
      });
    }
  });
}