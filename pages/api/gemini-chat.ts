import type { NextApiRequest, NextApiResponse } from "next";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { auth } from "@/lib/firebase-admin";

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

interface ChatRequest {
  message: string;
  attachments?: Array<{
    id: string;
    name: string;
    content?: string;
    type: string;
  }>;
  conversationHistory?: Array<{
    role: "user" | "assistant";
    content: string;
  }>;
}

interface ChatResponse {
  response?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChatResponse>
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const idToken = authHeader.split(" ")[1];

  try {
    await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Authentication failed" });
  }

  const { message, attachments = [], conversationHistory = [] }: ChatRequest = req.body;

  if (!message && attachments.length === 0) {
    return res.status(400).json({ error: "Message or attachments required" });
  }

  try {
    // Initialize the model
    const model = genAI.getGenerativeModel({
      model: "gemini-2.5-flash-lite-preview-06-17",
      generationConfig: {
        maxOutputTokens: 2048,
        temperature: 0.3,
        topP: 0.8,
        topK: 40,
      }
    });

    // Build the system context
    const systemContext = `You are knowledgeable cybersecurity AI assistant specializing in penetration testing and security analysis. You communicate in a conversational, approachable manner while maintaining technical accuracy.

Your expertise includes:
- Analyzing pentest scan reports and security documents
- Identifying vulnerabilities and security issues
- Providing clear remediation recommendations
- Explaining security concepts in an accessible way
- Offering guidance on cybersecurity tools and best practices

Communication style:
- Use a warm, conversational tone like you're talking to a colleague
- Use markdown formatting to enhance readability when appropriate
- Use **bold** for important terms, vulnerabilities, or key points
- Use \`code blocks\` for technical commands, file paths, or code snippets
- Use bullet points or numbered lists when listing items or steps
- Use headers (##) to organize longer responses into sections
- Use > blockquotes for important warnings or notes
- When discussing technical details, explain them clearly without being condescending
- If you're uncertain about something, acknowledge it honestly
- Provide practical, actionable advice with proper formatting for clarity

Remember to be helpful, patient, and thorough in your responses while using markdown to make them more readable and organized.

`;

    // Process attachments
    let attachmentContext = "";
    if (attachments.length > 0) {
      attachmentContext = "\nAttached files content:\n";
      attachments.forEach(attachment => {
        if (attachment.content) {
          attachmentContext += `\n--- File: ${attachment.name} (${attachment.type}) ---\n`;
          attachmentContext += attachment.content;
          attachmentContext += "\n--- End of file ---\n";
        }
      });
    }

    // Add conversation history for context (only if we have history)
    let conversationContext = "";
    if (conversationHistory.length > 0) {
      conversationContext = "\nPrevious conversation:\n";
      conversationHistory.slice(-5).forEach(msg => {
        conversationContext += `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}\n`;
      });
      conversationContext += "\n";
    }

    // Construct the full user message
    const fullUserMessage = systemContext + conversationContext + attachmentContext + "\nCurrent user message: " + message;

    // Generate streaming response
    const result = await model.generateContentStream(fullUserMessage);

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Transfer-Encoding', 'chunked');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // Disable nginx buffering
    res.setHeader('Access-Control-Allow-Origin', '*');

    let fullResponse = '';
    let buffer = '';

    for await (const chunk of result.stream) {
      const chunkText = chunk.text();
      if (chunkText) {
        fullResponse += chunkText;
        buffer += chunkText;

        // Check if we have complete sentences or lines to send
        const sentences = buffer.split(/([.!?]\s+|\n)/);

        // Send complete sentences/lines, keep the last incomplete one
        if (sentences.length > 1) {
          const toSend = sentences.slice(0, -1).join('');
          buffer = sentences[sentences.length - 1];

          if (toSend) {
            res.write(toSend);

            // Force flush the response to ensure immediate delivery
            if ((res as any).flush) {
              (res as any).flush();
            }

            // Add a small delay to make streaming more visible in development
            if (process.env.NODE_ENV === 'development') {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }
        }
      }
    }

    // Send any remaining content in buffer
    if (buffer) {
      res.write(buffer);
      if ((res as any).flush) {
        (res as any).flush();
      }
    }

    res.end();

    if (!fullResponse) {
      throw new Error("No response generated");
    }

  } catch (error: any) {
    console.error("Error generating Gemini response:", error);

    // Handle specific Gemini API errors
    if (error.message?.includes("API key")) {
      return res.status(500).json({
        error: "AI service configuration error. Please contact support."
      });
    }

    if (error.message?.includes("quota") || error.message?.includes("limit")) {
      return res.status(429).json({
        error: "AI service is temporarily busy. Please try again in a moment."
      });
    }

    if (error.message?.includes("safety")) {
      return res.status(400).json({
        error: "Content was blocked by safety filters. Please rephrase your request."
      });
    }

    return res.status(500).json({
      error: "Failed to generate AI response. Please try again."
    });
  }
}